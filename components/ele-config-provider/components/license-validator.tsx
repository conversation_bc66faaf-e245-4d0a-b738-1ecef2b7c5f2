import { defineComponent, reactive, computed, provide, watch } from 'vue';
import EleWatermark from '../../ele-watermark/index.vue';
import { useReceiver, VAL_KEY } from '../receiver';

// 新的License验证系统
interface LicenseData {
  product: string;
  version: string;
  licenseId: string;
  issuedAt: number;
  expiration?: number;
  domain?: string;
  features: string[];
  maxUsers?: number;
  signature: string;
}

// AES加密解密工具
const CryptoUtils = {
  // 简化的AES解密实现（实际项目中应使用crypto-js等库）
  decrypt(encryptedData: string, key: string): string {
    try {
      // Base64解码
      const decoded = atob(encryptedData);
      // 简单的XOR解密（实际应使用AES）
      let result = '';
      for (let i = 0; i < decoded.length; i++) {
        result += String.fromCharCode(
          decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        );
      }
      return result;
    } catch (e) {
      throw new Error('解密失败');
    }
  },

  // 验证签名
  verifySignature(data: string, signature: string, publicKey: string): boolean {
    // 简化的签名验证（实际应使用RSA等算法）
    const expectedSignature = btoa(data + publicKey).slice(0, 32);
    return signature === expectedSignature;
  }
};

// 常量定义
const PRODUCT_NAME = 'EleAdminPlusUltra';
const CURRENT_VERSION = '2.0';
const ENCRYPTION_KEY = 'EleAdminPlus2024SecretKey';
const PUBLIC_KEY = 'EleAdminPlusPublicKey2024';

// 错误消息
const ERROR_MESSAGES = {
  TITLE: '🔐 EleAdminPlus Ultra License 验证',
  NO_LICENSE: '❌ 未找到有效的License，请联系管理员获取授权',
  INVALID_FORMAT: '❌ License格式无效，请检查License是否正确',
  WRONG_PRODUCT: '❌ License产品不匹配，当前产品需要EleAdminPlusUltra授权',
  WRONG_VERSION: '❌ License版本不匹配，当前版本需要2.0授权',
  EXPIRED: '❌ License已过期，请续费或联系管理员',
  DOMAIN_MISMATCH: '❌ 域名不匹配，此License仅限指定域名使用',
  INVALID_SIGNATURE: '❌ License签名验证失败，可能已被篡改',
  FEATURE_NOT_ALLOWED: '❌ 当前功能未授权，请升级License',
  USER_LIMIT_EXCEEDED: '❌ 用户数量超出License限制'
};

const WATERMARK_CONTENT = 'ELE ADMIN PLUS ULTRA - UNLICENSED';

export default defineComponent({
  name: 'LicenseValidator',
  props: {
    wrapPosition: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { slots }) {
    const PRINT_ERROR = (errorType: string, details?: any) => {
      const divider = new Array(60).join('*');
      const tips = [divider];
      tips.push(ERROR_MESSAGES.TITLE);
      tips.push(ERROR_MESSAGES[errorType as keyof typeof ERROR_MESSAGES] || errorType);
      if (details) {
        tips.push(`详细信息: ${JSON.stringify(details)}`);
      }
      tips.push(divider);
      console.error(tips.join('\n'));
    };

    const globalConfig = useReceiver();
    const result = reactive<any>({});
    
    const code = computed<string | undefined>(() => {
      const val = globalConfig.license;
      return val ? val.trim() : void 0;
    });

    provide<any>(VAL_KEY, result);

    const clearResult = (obj: any, forbidden?: boolean) => {
      Object.keys(result).forEach((k) => {
        result[k] = void 0;
      });
      Object.assign(result, { ...obj, forbidden: !!forbidden });
    };

    const validateLicense = (licenseCode: string): LicenseData | null => {
      try {
        // 解密license
        const decryptedData = CryptoUtils.decrypt(licenseCode, ENCRYPTION_KEY);
        const licenseData: LicenseData = JSON.parse(decryptedData);

        // 验证产品名称
        if (licenseData.product !== PRODUCT_NAME) {
          PRINT_ERROR('WRONG_PRODUCT', { expected: PRODUCT_NAME, actual: licenseData.product });
          return null;
        }

        // 验证版本
        if (licenseData.version !== CURRENT_VERSION) {
          PRINT_ERROR('WRONG_VERSION', { expected: CURRENT_VERSION, actual: licenseData.version });
          return null;
        }

        // 验证过期时间
        if (licenseData.expiration && licenseData.expiration < Date.now() / 1000) {
          const expiredDate = new Date(licenseData.expiration * 1000).toLocaleString();
          PRINT_ERROR('EXPIRED', { expiredAt: expiredDate });
          return null;
        }

        // 验证域名
        if (licenseData.domain) {
          const hostname = window?.location?.hostname;
          if (!hostname) {
            PRINT_ERROR('DOMAIN_MISMATCH', { expected: licenseData.domain, actual: 'unknown' });
            return null;
          }

          // 允许localhost和127.0.0.1用于开发
          if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
            const domainParts = licenseData.domain.split('.');
            const hostParts = hostname.split('.');
            
            // 检查域名匹配
            let isMatch = true;
            for (let i = domainParts.length - 1; i >= 0; i--) {
              if (domainParts[i] !== hostParts[hostParts.length - domainParts.length + i]) {
                isMatch = false;
                break;
              }
            }

            if (!isMatch) {
              PRINT_ERROR('DOMAIN_MISMATCH', { expected: licenseData.domain, actual: hostname });
              return null;
            }
          }
        }

        // 验证签名
        const dataToVerify = JSON.stringify({
          product: licenseData.product,
          version: licenseData.version,
          licenseId: licenseData.licenseId,
          issuedAt: licenseData.issuedAt,
          expiration: licenseData.expiration,
          domain: licenseData.domain,
          features: licenseData.features,
          maxUsers: licenseData.maxUsers
        });

        if (!CryptoUtils.verifySignature(dataToVerify, licenseData.signature, PUBLIC_KEY)) {
          PRINT_ERROR('INVALID_SIGNATURE');
          return null;
        }

        return licenseData;
      } catch (e) {
        PRINT_ERROR('INVALID_FORMAT', { error: e.message });
        return null;
      }
    };

    watch(
      code,
      (val) => {
        if (typeof val !== 'string' || !val) {
          clearResult({}, true);
          PRINT_ERROR('NO_LICENSE');
          return;
        }

        const licenseData = validateLicense(val);
        if (licenseData) {
          clearResult(licenseData, false);
          console.log('✅ License验证成功', licenseData);
        } else {
          clearResult({}, true);
        }
      },
      { immediate: true }
    );

    return () => (
      <EleWatermark
        wrapPosition={false}
        style={
          !props.wrapPosition || result.forbidden
            ? void 0
            : { position: 'relative' }
        }
        disabled={!result.forbidden}
        content={WATERMARK_CONTENT}
      >
        {slots.default?.(result)}
      </EleWatermark>
    );
  }
});

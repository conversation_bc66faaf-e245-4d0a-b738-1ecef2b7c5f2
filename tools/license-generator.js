#!/usr/bin/env node

/**
 * EleAdminPlus Ultra License 生成工具
 * 使用方法: node license-generator.js [options]
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// 配置常量
const PRODUCT_NAME = 'EleAdminPlusUltra';
const CURRENT_VERSION = '2.0';
const ENCRYPTION_KEY = 'EleAdminPlus2024SecretKey';
const PUBLIC_KEY = 'EleAdminPlusPublicKey2024';

// 加密工具
const CryptoUtils = {
  // 简化的AES加密实现
  encrypt(data, key) {
    let result = '';
    for (let i = 0; i < data.length; i++) {
      result += String.fromCharCode(
        data.charCodeAt(i) ^ key.charCodeAt(i % key.length)
      );
    }
    return Buffer.from(result).toString('base64');
  },

  // 生成签名
  generateSignature(data, privateKey) {
    // 简化的签名生成（实际应使用RSA等算法）
    return Buffer.from(data + privateKey).toString('base64').slice(0, 32);
  }
};

// License生成器
class LicenseGenerator {
  constructor() {
    this.defaultConfig = {
      product: PRODUCT_NAME,
      version: CURRENT_VERSION,
      features: ['basic'],
      maxUsers: 10
    };
  }

  // 生成License ID
  generateLicenseId() {
    return crypto.randomUUID();
  }

  // 生成License
  generateLicense(options = {}) {
    const config = { ...this.defaultConfig, ...options };
    
    // 验证必要参数
    if (!config.domain && !config.isDevelopment) {
      throw new Error('必须指定domain或设置isDevelopment为true');
    }

    const licenseData = {
      product: config.product,
      version: config.version,
      licenseId: config.licenseId || this.generateLicenseId(),
      issuedAt: Math.floor(Date.now() / 1000),
      expiration: config.expiration,
      domain: config.domain,
      features: config.features,
      maxUsers: config.maxUsers
    };

    // 生成签名
    const dataToSign = JSON.stringify({
      product: licenseData.product,
      version: licenseData.version,
      licenseId: licenseData.licenseId,
      issuedAt: licenseData.issuedAt,
      expiration: licenseData.expiration,
      domain: licenseData.domain,
      features: licenseData.features,
      maxUsers: licenseData.maxUsers
    });

    licenseData.signature = CryptoUtils.generateSignature(dataToSign, PUBLIC_KEY);

    // 加密License数据
    const licenseJson = JSON.stringify(licenseData);
    const encryptedLicense = CryptoUtils.encrypt(licenseJson, ENCRYPTION_KEY);

    return {
      license: encryptedLicense,
      data: licenseData
    };
  }

  // 生成开发License
  generateDevelopmentLicense() {
    return this.generateLicense({
      isDevelopment: true,
      features: ['basic', 'advanced', 'premium'],
      maxUsers: 999,
      expiration: Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60) // 1年后过期
    });
  }

  // 生成生产License
  generateProductionLicense(domain, options = {}) {
    return this.generateLicense({
      domain,
      ...options
    });
  }

  // 生成永久License
  generatePermanentLicense(domain, options = {}) {
    return this.generateLicense({
      domain,
      expiration: null, // 永不过期
      ...options
    });
  }
}

// 命令行工具
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {};
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--domain':
      case '-d':
        options.domain = args[++i];
        break;
      case '--features':
      case '-f':
        options.features = args[++i].split(',');
        break;
      case '--max-users':
      case '-u':
        options.maxUsers = parseInt(args[++i]);
        break;
      case '--expiration':
      case '-e':
        const days = parseInt(args[++i]);
        options.expiration = Math.floor(Date.now() / 1000) + (days * 24 * 60 * 60);
        break;
      case '--development':
      case '--dev':
        options.isDevelopment = true;
        break;
      case '--permanent':
      case '-p':
        options.permanent = true;
        break;
      case '--output':
      case '-o':
        options.output = args[++i];
        break;
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
    }
  }
  
  return options;
}

function showHelp() {
  console.log(`
EleAdminPlus Ultra License 生成工具

使用方法:
  node license-generator.js [options]

选项:
  -d, --domain <domain>        指定授权域名
  -f, --features <features>    指定功能模块 (用逗号分隔)
  -u, --max-users <number>     指定最大用户数
  -e, --expiration <days>      指定过期天数
  -p, --permanent              生成永久License
  --dev, --development         生成开发License
  -o, --output <file>          输出到文件
  -h, --help                   显示帮助信息

示例:
  # 生成开发License
  node license-generator.js --dev

  # 生成生产License
  node license-generator.js -d example.com -f basic,advanced -u 50 -e 365

  # 生成永久License
  node license-generator.js -d example.com --permanent -f basic,advanced,premium

  # 输出到文件
  node license-generator.js --dev -o license.txt
`);
}

// 主函数
function main() {
  try {
    const options = parseArgs();
    const generator = new LicenseGenerator();
    
    let result;
    
    if (options.isDevelopment) {
      result = generator.generateDevelopmentLicense();
      console.log('🔧 生成开发License成功!');
    } else if (options.permanent) {
      if (!options.domain) {
        console.error('❌ 生成永久License需要指定域名');
        process.exit(1);
      }
      result = generator.generatePermanentLicense(options.domain, options);
      console.log('♾️  生成永久License成功!');
    } else {
      if (!options.domain) {
        console.error('❌ 生成生产License需要指定域名');
        process.exit(1);
      }
      result = generator.generateProductionLicense(options.domain, options);
      console.log('🏭 生成生产License成功!');
    }

    console.log('\n📋 License信息:');
    console.log('License ID:', result.data.licenseId);
    console.log('产品:', result.data.product);
    console.log('版本:', result.data.version);
    console.log('域名:', result.data.domain || '不限制');
    console.log('功能:', result.data.features.join(', '));
    console.log('最大用户数:', result.data.maxUsers);
    console.log('签发时间:', new Date(result.data.issuedAt * 1000).toLocaleString());
    console.log('过期时间:', result.data.expiration ? new Date(result.data.expiration * 1000).toLocaleString() : '永不过期');

    console.log('\n🔑 License密钥:');
    console.log(result.license);

    // 输出到文件
    if (options.output) {
      const outputData = {
        license: result.license,
        info: result.data,
        generatedAt: new Date().toISOString()
      };
      
      fs.writeFileSync(options.output, JSON.stringify(outputData, null, 2));
      console.log(`\n💾 License已保存到: ${options.output}`);
    }

    console.log('\n📝 使用方法:');
    console.log('将上面的License密钥复制到.env文件中的VITE_LICENSE变量');

  } catch (error) {
    console.error('❌ 生成License失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { LicenseGenerator, CryptoUtils };
